<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login/Signup Popup Forms</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Buttons to trigger popups -->
        <button class="trigger-btn" onclick="showLoginForm()">Login</button>
        <button class="trigger-btn" onclick="showSignupForm()">Signup</button>
    </div>

    <!-- Login Popup -->
    <div id="loginPopup" class="popup">
        <div class="form-container">
            <h2>Login Form</h2>
            <div class="tab-buttons">
                <button class="tab-btn active">Login</button>
                <button class="tab-btn" onclick="showSignupForm()">Signup</button>
            </div>
            <form class="form">
                <input type="email" placeholder="Email Address" required>
                <input type="password" placeholder="Password" required>
                <a href="#" class="forgot-password">Forgot password?</a>
                <button type="submit" class="submit-btn">Login</button>
                <p class="switch-text">Not a member? <a href="#" onclick="showSignupForm()">Signup now</a></p>
            </form>
        </div>
    </div>

    <!-- Signup Popup -->
    <div id="signupPopup" class="popup">
        <div class="form-container">
            <h2>Signup Form</h2>
            <div class="tab-buttons">
                <button class="tab-btn" onclick="showLoginForm()">Login</button>
                <button class="tab-btn active">Signup</button>
            </div>
            <form class="form">
                <input type="email" placeholder="Email Address" required>
                <input type="password" placeholder="Password" required>
                <input type="password" placeholder="Confirm password" required>
                <button type="submit" class="submit-btn">Signup</button>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>



