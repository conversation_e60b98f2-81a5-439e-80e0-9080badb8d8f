
import React from 'react';
import { User, MapPin, Code, Trophy, UserPlus, Brain, Sparkles } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { MatchResult } from '@/utils/smartMatching';

interface SmartMatchesProps {
  matches: MatchResult[];
  onConnect: (userId: string) => void;
  onViewProfile: (userId: string) => void;
}

const SmartMatches = ({ matches, onConnect, onViewProfile }: SmartMatchesProps) => {
  if (matches.length === 0) {
    return (
      <div className="text-center py-12">
        <Brain className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">No matches found</h3>
        <p className="text-gray-500">Try adjusting your search criteria to find potential teammates.</p>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 60) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (score >= 40) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
          <Sparkles className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-800">Smart Matches</h3>
          <p className="text-sm text-gray-600">AI-powered teammate recommendations based on your profile</p>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {matches.map((match, index) => (
          <Card 
            key={match.profile.id} 
            className="hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 shadow-lg overflow-hidden relative"
          >
            {/* Match Score Badge */}
            <div className="absolute top-3 right-3 z-10">
              <Badge className={`${getScoreColor(match.score)} border font-semibold`}>
                {Math.round(match.score)}% match
              </Badge>
            </div>

            {/* Rank Badge for top matches */}
            {index < 3 && (
              <div className="absolute top-3 left-3 z-10">
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                  #{index + 1}
                </Badge>
              </div>
            )}

            <CardHeader className="text-center pb-4 pt-8">
              <Avatar className="w-20 h-20 mx-auto mb-4">
                <AvatarImage src={match.profile.avatar} alt={match.profile.name} />
                <AvatarFallback className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-lg">
                  {match.profile.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <h3 className="text-xl font-semibold">{match.profile.name}</h3>
              <div className="flex items-center justify-center text-sm text-gray-600 mb-2">
                <MapPin className="w-4 h-4 mr-1" />
                {match.profile.college}
              </div>
              <p className="text-sm text-gray-500">{match.profile.branch}</p>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Match Reasons */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Brain className="w-4 h-4 mr-2 text-purple-500" />
                  <span className="text-sm font-medium text-purple-700">Why this match?</span>
                </div>
                <div className="space-y-1">
                  {match.matchReasons.slice(0, 2).map((reason, idx) => (
                    <p key={idx} className="text-xs text-gray-600 bg-purple-50 px-2 py-1 rounded">
                      {reason}
                    </p>
                  ))}
                </div>
              </div>

              {/* Skills */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Code className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Skills</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {match.profile.skills?.slice(0, 3).map((skill, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                  {match.profile.skills && match.profile.skills.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{match.profile.skills.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>

              {/* Achievements */}
              {match.profile.achievements && (
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <Trophy className="w-4 h-4 mr-2 text-gray-500" />
                    <span className="text-sm font-medium">Achievements</span>
                  </div>
                  <p className="text-sm text-gray-600">{match.profile.achievements}</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => onViewProfile(match.profile.id)}
                >
                  <User className="w-4 h-4 mr-1" />
                  Profile
                </Button>
                <Button 
                  size="sm" 
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                  onClick={() => onConnect(match.profile.id)}
                >
                  <UserPlus className="w-4 h-4 mr-1" />
                  Connect
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SmartMatches;
