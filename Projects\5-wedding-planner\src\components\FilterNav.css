.filter-nav {
  display: flex;
  gap: 15px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  margin: 20px auto;
  overflow-x: auto;
  white-space: nowrap;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 1200px;
  justify-content: center;
}

.filter-select {
  padding: 8px 16px;
  border: 1px solid rgba(179, 139, 109, 0.3);  /* Warm brown */
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  font-size: 0.9rem;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-select:hover {
  border-color: #d4af37;  /* Gold color */
  background: white;
}

.filter-select option {
  background: #2c3e50;
  color: white;
  padding: 10px;
}

/* Make the navbar scrollable on mobile */
@media (max-width: 768px) {
  .filter-nav {
    padding: 10px;
    gap: 10px;
  }

  .filter-select {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* Custom scrollbar for the filter nav */
.filter-nav::-webkit-scrollbar {
  height: 6px;
}

.filter-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.filter-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.filter-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

