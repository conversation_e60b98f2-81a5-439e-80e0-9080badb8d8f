body {
    margin: 0;
    font-family: Arial, sans-serif;
    background-color: #b3b3e6;
}

.popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.form-container {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab-buttons {
    display: flex;
    margin-bottom: 20px;
    border-radius: 25px;
    background-color: #f5f5f5;
    padding: 5px;
}

.tab-btn {
    flex: 1;
    padding: 10px;
    border: none;
    background: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 16px;
}

.tab-btn.active {
    background-color: #0052cc;
    color: white;
}

.form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

input {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 14px;
}

.forgot-password {
    color: #0052cc;
    text-decoration: none;
    font-size: 14px;
    text-align: left;
}

.submit-btn {
    background-color: #0052cc;
    color: white;
    padding: 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.switch-text {
    text-align: center;
    font-size: 14px;
    margin-top: 10px;
}

.switch-text a {
    color: #0052cc;
    text-decoration: none;
}