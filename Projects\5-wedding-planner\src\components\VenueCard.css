.venue-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  width: 100%;
  max-width: 350px;
  margin: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.venue-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.venue-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.image-slider {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.venue-image img {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-dots {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}

/* Rename to carousel-dot to avoid conflicts with loading screen dots */
.carousel-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
}

.carousel-dot.active {
  background: white;
  /* Remove transform scale */
}

/* Remove hover effects */
.image-slider {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease-in-out;
}

.venue-info {
  padding: 15px 15px 20px;
}

.venue-info h3 {
  margin: 0 0 10px 0;
  font-size: 1.2rem;
}

.location {
  color: #666;
  margin: 5px 0;
  font-size: 0.9rem;
}

.description {
  color: #444;
  font-size: 0.9rem;
  margin: 10px 0;
}

.stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.rating {
  color: #666;
  font-size: 0.9rem;
}

.price {
  font-weight: bold;
  color: #ff4b6e;
}

.tag {
  position: absolute;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  color: white;
  top: 10px;
  z-index: 2;
  transition: all 0.3s ease;
}

.most-booked {
  background-color: #d4af37;  /* Gold color */
  right: 10px;
}

.most-liked {
  background-color: #b38b6d;  /* Warm brown */
  right: 110px;
}

.most-booked:hover, .most-liked:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

@media (max-width: 480px) {
  .most-liked {
    right: 110px;
  }
  
  .tag {
    padding: 4px 10px;
    font-size: 0.7rem;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.book-button, .book-now-btn {
  background-color: #d4af37;  /* Gold color */
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.book-button:hover, .book-now-btn:hover {
  background-color: #b38b6d;  /* Warm brown */
}

.book-now-btn {
  width: 100%;
  padding: 12px 24px;
  margin-top: 15px;
  background-color: #d4af37;  /* Gold color */
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.book-now-btn:hover {
  background-color: #b38b6d;  /* Warm brown */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.book-now-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.venue-details {
  display: flex;
  gap: 10px;
  margin: 8px 0;
}

.venue-type, .venue-space {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  color: white;
  text-transform: capitalize;
}

.venue-type {
  background-color: #b38b6d; /* Warm brown */
}

.venue-space {
  background-color: #8b6d4f; /* Darker brown */
}

/* Capitalize first letter of venue type and space */
.venue-type::first-letter, .venue-space::first-letter {
  text-transform: uppercase;
}

