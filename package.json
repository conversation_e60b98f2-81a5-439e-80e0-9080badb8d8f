{"devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "framer-motion": "^12.19.2", "lucide-react": "^0.525.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.3", "swiper": "^11.2.10", "yup": "^1.6.1", "zustand": "^5.0.6"}, "name": "react", "version": "1.0.0", "main": "script.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": ""}