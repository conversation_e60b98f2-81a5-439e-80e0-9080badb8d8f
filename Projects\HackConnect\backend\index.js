const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
require("dotenv").config();

const app = express();
app.use(cors());
app.use(express.json());

mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
}).then(() => console.log("MongoDB connected"))
  .catch((err) => console.error("MongoDB connection error:", err));

// Example model
const Team = require("./models/Team");

app.post("/teams", async (req, res) => {
  const { name, members } = req.body;
  try {
    const newTeam = new Team({ name, members });
    await newTeam.save();
    res.status(201).json(newTeam);
  } catch (err) {
    res.status(500).json({ error: "Failed to create team" });
  }
});

app.get("/", (req, res) => res.send("Backend running"));
app.listen(process.env.PORT || 5000, () => console.log("Server running on port", process.env.PORT || 5000));
