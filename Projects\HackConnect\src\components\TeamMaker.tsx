import React, { useState } from 'react';
import { <PERSON>, Search, Filter, Loader2, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import SmartMatches from './SmartMatches';
import { findSmartMatches, UserProfile } from '@/utils/smartMatching';

// Mock data for demonstration
const mockCandidates: UserProfile[] = [
  {
    id: '1',
    name: "<PERSON>",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    college: "Stanford University",
    state: "California",
    branch: "Computer Science",
    skills: ["React", "Node.js", "Python", "AI/ML"],
    achievements: "3x Hackathon Winner",
    status: "available",
    mutualInterests: ["AI", "Web Development"]
  },
  {
    id: '2',
    name: "<PERSON>",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    college: "MIT",
    state: "Massachusetts",
    branch: "Electrical Engineering",
    skills: ["UI/UX", "Figma", "Frontend", "Mobile"],
    achievements: "Design Award Winner",
    status: "available",
    mutualInterests: ["Design", "Mobile Apps"]
  },
  {
    id: '3',
    name: "David Park",
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    college: "UC Berkeley",
    state: "California",
    branch: "Information Technology",
    skills: ["Java", "Spring", "Docker", "AWS"],
    achievements: "Backend Specialist",
    status: "available",
    mutualInterests: ["Cloud", "Backend"]
  },
  {
    id: '4',
    name: "Emily Rodriguez",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    college: "Caltech",
    state: "California",
    branch: "Computer Science",
    skills: ["Python", "TensorFlow", "Data Science", "R"],
    achievements: "ML Competition Winner",
    status: "available",
    mutualInterests: ["Machine Learning", "Data"]
  },
  {
    id: '5',
    name: "Michael Kim",
    avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
    college: "Carnegie Mellon",
    state: "Pennsylvania",
    branch: "Software Engineering",
    skills: ["React Native", "Flutter", "iOS", "Android"],
    achievements: "Mobile App Developer",
    status: "available",
    mutualInterests: ["Mobile Development"]
  },
  {
    id: '6',
    name: "Lisa Wang",
    avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
    college: "Stanford University",
    state: "California",
    branch: "Computer Science",
    skills: ["Blockchain", "Solidity", "Web3", "DeFi"],
    achievements: "Crypto Hackathon Winner",
    status: "available",
    mutualInterests: ["Blockchain", "Web3"]
  }
];

const TeamMaker = () => {
  const [college, setCollege] = useState('');
  const [state, setState] = useState('');
  const [skill, setSkill] = useState('');
  const [branch, setBranch] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [smartMatches, setSmartMatches] = useState<any[]>([]);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async () => {
    setIsSearching(true);
    setHasSearched(true);
    console.log('Searching for teammates with:', { college, state, skill, branch });
    
    // Simulate API call
    setTimeout(() => {
      const userProfile = {
        college: college || undefined,
        state: state || undefined,
        skills: skill ? [skill] : undefined,
        branch: branch || undefined
      };

      const matches = findSmartMatches(userProfile, mockCandidates, 6);
      setSmartMatches(matches);
      setIsSearching(false);
    }, 2000);
  };

  const handleConnect = (userId: string) => {
    console.log('Connecting with user:', userId);
    // TODO: Implement connection logic
  };

  const handleViewProfile = (userId: string) => {
    console.log('Viewing profile:', userId);
    // TODO: Implement profile view logic
  };

  return (
    <div className="max-w-6xl mx-auto mb-16">
      <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
        <CardHeader className="text-center">
          <div className="inline-flex items-center bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full mb-4 mx-auto">
            <Brain className="w-5 h-5 mr-2" />
            AI Team Maker
          </div>
          <CardTitle className="text-2xl lg:text-3xl">Smart Teammate Matching</CardTitle>
          <p className="text-gray-600 mt-2">Let our AI find the perfect teammates for your hackathon team</p>
        </CardHeader>
        
        <CardContent className="p-6 lg:p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6 mb-8">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">College / University</label>
              <Select value={college} onValueChange={setCollege}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="Select your college" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="stanford">Stanford University</SelectItem>
                  <SelectItem value="mit">MIT</SelectItem>
                  <SelectItem value="berkeley">UC Berkeley</SelectItem>
                  <SelectItem value="caltech">Caltech</SelectItem>
                  <SelectItem value="cmu">Carnegie Mellon</SelectItem>
                  <SelectItem value="harvard">Harvard University</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">State / Region</label>
              <Select value={state} onValueChange={setState}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="Select your state" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="california">California</SelectItem>
                  <SelectItem value="newyork">New York</SelectItem>
                  <SelectItem value="texas">Texas</SelectItem>
                  <SelectItem value="massachusetts">Massachusetts</SelectItem>
                  <SelectItem value="washington">Washington</SelectItem>
                  <SelectItem value="florida">Florida</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Skill Needed</label>
              <Select value={skill} onValueChange={setSkill}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="What skill do you need?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Frontend Developer">Frontend Developer</SelectItem>
                  <SelectItem value="Backend Developer">Backend Developer</SelectItem>
                  <SelectItem value="Full Stack Developer">Full Stack Developer</SelectItem>
                  <SelectItem value="AI/ML Engineer">AI/ML Engineer</SelectItem>
                  <SelectItem value="UI/UX Designer">UI/UX Designer</SelectItem>
                  <SelectItem value="DevOps Engineer">DevOps Engineer</SelectItem>
                  <SelectItem value="Mobile Developer">Mobile Developer</SelectItem>
                  <SelectItem value="Data Scientist">Data Scientist</SelectItem>
                  <SelectItem value="Blockchain Developer">Blockchain Developer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Branch / Department</label>
              <Select value={branch} onValueChange={setBranch}>
                <SelectTrigger className="h-12">
                  <SelectValue placeholder="Select your branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Computer Science">Computer Science</SelectItem>
                  <SelectItem value="Information Technology">Information Technology</SelectItem>
                  <SelectItem value="Electronics & Communication">Electronics & Communication</SelectItem>
                  <SelectItem value="Electrical Engineering">Electrical Engineering</SelectItem>
                  <SelectItem value="Mechanical Engineering">Mechanical Engineering</SelectItem>
                  <SelectItem value="Civil Engineering">Civil Engineering</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Search Results Preview */}
          {(college || state || skill || branch) && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Search Criteria:</h4>
              <div className="flex flex-wrap gap-2">
                {college && <Badge variant="secondary">College: {college}</Badge>}
                {state && <Badge variant="secondary">State: {state}</Badge>}
                {skill && <Badge variant="secondary">Skill: {skill}</Badge>}
                {branch && <Badge variant="secondary">Branch: {branch}</Badge>}
              </div>
            </div>
          )}

          <div className="text-center">
            <Button 
              onClick={handleSearch}
              disabled={isSearching || (!college && !state && !skill && !branch)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 text-lg rounded-full transition-all duration-300 hover:scale-105 w-full sm:w-auto"
            >
              {isSearching ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Finding Smart Matches...
                </>
              ) : (
                <>
                  <Brain className="w-5 h-5 mr-2" />
                  Find Smart Matches
                </>
              )}
            </Button>
          </div>

          {isSearching && (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">Our AI is analyzing profiles to find your perfect teammates...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Smart Matches Results */}
      {hasSearched && !isSearching && (
        <div className="mt-8">
          <SmartMatches 
            matches={smartMatches}
            onConnect={handleConnect}
            onViewProfile={handleViewProfile}
          />
        </div>
      )}
    </div>
  );
};

export default TeamMaker;
