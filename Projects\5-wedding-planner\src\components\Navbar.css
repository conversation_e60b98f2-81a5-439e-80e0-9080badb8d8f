
@import url('https://fonts.googleapis.com/css2?family=Great+Vibes&family=Italianno&family=Playfair+Display:wght@500&display=swap');

.navbar {
  padding: 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(2px);
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  width: 100%;
  height: 30vh; /* Set height to 30% of viewport height */
  box-sizing: border-box;
  background-image: url('https://thumbs.dreamstime.com/z/magnificent-wedding-hall-dazzling-crystal-chandelier-featuring-beautifully-set-tables-dance-floor-image-331833135.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  z-index: 100;
  border-radius: 0;
  margin: 0;
}

/* Remove any potential body/html default margins */
body, html {
  margin: 0;
  padding: 0;
}

.app {
  margin: 0;
  padding: 0;
}

/* Further reduced overlay opacity */
.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2); /* Reduced from 0.4 to 0.2 */
  z-index: -1;
  border-radius: 10px;
}

/* Enhanced text contrast even more for better visibility */
.navbar h1 {
  color: #000000;
  position: relative;
  z-index: 2;
  text-shadow: 2px 2px 6px rgba(255, 255, 255, 1), 
               -1px -1px 4px rgba(255, 255, 255, 1); /* Enhanced text shadow */
  font-weight: 800;
  font-size: 500px; /* Increased font weight */
}

.menu-right {
  justify-self: end;
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  z-index: 2;
  margin-right: 20px; /* Added right margin */
}

.navbar h1 {
  font-family: "Italianno", cursive;
  font-weight: 800;
  font-style: normal;
  font-size: 100px !important; /* Changed to 100px with !important */
  color: #8B0000;
  text-shadow: 
    3px 3px 6px rgba(0, 0, 0, 0.5),
    -2px -2px 4px rgba(255, 255, 255, 0.9),
    2px 2px 8px rgba(139, 0, 0, 0.6),
    0px 0px 15px rgba(255, 255, 255, 0.5);
  margin: 0;
  grid-column: 2;
  text-align: center;
  letter-spacing: 3px;
  -webkit-text-stroke: 1px #4a0000;
  position: relative;
  z-index: 2;
}

/* Remove ALL media queries that affect font-size */
@media screen and (min-width: 1600px) {
  .navbar {
    height: 30vh;
  }
}

@media screen and (min-width: 1920px) {
  .navbar {
    height: 30vh;
  }
}

@media screen and (max-width: 1400px) {
  .navbar {
    height: 30vh;
  }
}

@media screen and (max-width: 1200px) {
  .navbar {
    height: 30vh;
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    height: 25vh;
    padding: 15px 20px;
  }
}

@media screen and (max-width: 480px) {
  .navbar {
    height: 20vh;
  }
}

/* Delete or comment out ALL other media queries that modify .navbar h1 font-size */

/* Add a subtle outline effect */
.navbar h1::before {
  content: attr(data-text);
  position: absolute;
  left: -2px;
  top: -2px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  z-index: -1;
}

/* Enhanced background overlay for better text visibility */
.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.3); /* Increased opacity */
  backdrop-filter: blur(3px); /* Increased blur */
  z-index: 1;
  border-radius: 10px;
}

/* Updated responsive breakpoints */
@media screen and (min-width: 1600px) {
  .navbar h1 {
    font-size: 500px;
  }
}

@media screen and (min-width: 1920px) {
  .navbar h1 {
    font-size: 500px;
  }
}

@media screen and (max-width: 1400px) {
  .navbar h1 {
    font-size: 500px;
  }
}

@media screen and (max-width: 1200px) {
  .navbar h1 {
    font-size: 500px;
  }
}

@media screen and (max-width: 768px) {
  .navbar h1 {
    font-size: 500px;
    
  }
  .navbar {
    height: 25vh;
  }
}

@media screen and (max-width: 480px) {
  .navbar h1 {
    font-size: 500px;
  }
  .navbar {
    height: 20vh;
  }
}

/* Large screens (above 1200px) */
@media screen and (min-width: 1201px) {
  .navbar h1 {
    font-size: 2.8rem;
  }
}

/* Medium-large screens */
@media screen and (max-width: 1200px) {
  .navbar h1 {
    font-size: 2.4rem;
  }
}

/* Medium screens */
@media screen and (max-width: 992px) {
  .navbar h1 {
    font-size: 2.2rem;
  }
}

/* Tablets */
@media screen and (max-width: 768px) {
  .navbar {
    padding: 15px 20px;
  }

  .navbar h1 {
    font-size: 500px;
  }
}

/* Large mobile phones */
@media screen and (max-width: 576px) {
  .navbar h1 {
    font-size: 500px;
  }
}

/* Small mobile phones */
@media screen and (max-width: 375px) {
  .navbar {
    padding: 12px 15px;
  }

  .navbar h1 {
    font-size: 1.5rem;
  }
}

/* Auth Buttons */
.auth-buttons {
  display: flex;
  gap: 10px;
  margin-right: 15px; /* Added right margin */
}

.auth-btn {
  padding: 8px 16px;
  font-size: 1rem;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-btn.login {
  background-color: white; /* Solid white background */
  color: #d4af37; /* Gold color */
  border: 2px solid #d4af37;
  padding: 10px 20px;
  margin-right: 5px; /* Added small gap between buttons */
}

.auth-btn.signup {
  background-color: #d4af37; /* Gold color */
  color: white;
  border: 2px solid #d4af37;
  padding: 10px 20px;
}

.auth-btn:hover {
  transform: scale(1.05);
  opacity: 0.9;
}

.auth-btn.login:hover {
  background-color: #f5f5f5; /* Light gray on hover */
  color: #b38b6d; /* Warm brown */
  border-color: #b38b6d;
}

.auth-btn.signup:hover {
  background-color: #b38b6d; /* Warm brown */
  border-color: #b38b6d;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  gap: 6px;
  cursor: pointer;
  padding: 10px;
  margin-right: 15px; /* Added right margin */
}

.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: white;
  border-radius: 3px;
  transition: all 0.3s ease;
}

/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1000;
}

.sidebar.open {
  right: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.sidebar-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.sidebar-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sidebar-content .auth-btn {
  width: 100%;
}

/* Overlay */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 101;
}

.overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
  .desktop-auth {
    display: none;
  }

  .hamburger {
    display: flex;
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    padding: 15px 20px;
  }

  .navbar h1 {
    font-size: 2rem;
  }
}

@media screen and (max-width: 576px) {
  .navbar h1 {
    font-size: 1.8rem;
  }
}

@media screen and (max-width: 375px) {
  .navbar {
    padding: 12px 15px;
  }

  .navbar h1 {
    font-size: 1.5rem;
  }
}
