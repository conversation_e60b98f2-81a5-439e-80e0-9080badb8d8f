@import url('https://fonts.googleapis.com/css2?family=Great+Vibes&family=Italianno&family=Playfair+Display:wght@500&display=swap');

body {
  font-family: 'Allura', cursive;
  background-color: #eeb04c;
  text-align: center;
  margin: 0;
  padding: 0;
}

.app {
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    #F7E7CE 0%,
    #f9efdd 50%,
    #F7E7CE 100%
  );
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.title {
  font-family: "Italianno", cursive;
  font-weight: 800;
  font-style: normal;
  font-size: clamp(60px, 15vw, 300px); /* Using clamp for responsive scaling */
  color: #8B0000;
  text-shadow: 
    3px 3px 6px rgba(0, 0, 0, 0.5),
    -2px -2px 4px rgba(255, 255, 255, 0.9),
    2px 2px 8px rgba(139, 0, 0, 0.6),
    0px 0px 15px rgba(255, 255, 255, 0.5);
  margin-bottom: 20px;
  letter-spacing: 3px;
  -webkit-text-stroke: 1px #4a0000;
  position: relative;
  display: inline-block;
}

/* Add new media query for larger screens */
@media screen and (min-width: 1600px) {
  .title {
    font-size: 200px;
  }
}

@media screen and (min-width: 1920px) {
  .title {
    font-size: 220px;
  }
}

/* Add outline effect */
.title::before {
  content: attr(data-text);
  position: absolute;
  left: -2px;
  top: -2px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  z-index: -1;
}

/* Updated responsive breakpoints */
@media screen and (max-width: 1400px) {
  .title, .navbar h1 {
    font-size: 220px;
    text-shadow: 
      2px 2px 4px rgba(0, 0, 0, 0.5),
      -1px -1px 3px rgba(255, 255, 255, 0.9),
      1px 1px 6px rgba(139, 0, 0, 0.6),
      0px 0px 12px rgba(255, 255, 255, 0.5);
  }
}

@media screen and (max-width: 1200px) {
  .title, .navbar h1 {
    font-size: 100px;
  }
}

@media screen and (max-width: 768px) {
  .title {
    font-size: clamp(40px, 12vw, 120px);
    letter-spacing: 2px;
  }
}

@media screen and (max-width: 480px) {
  .title {
    font-size: clamp(30px, 10vw, 80px);
    letter-spacing: 1.5px;
  }
}

.subtitle {
  font-family: 'Playfair Display', serif;
  font-size: 28px; /* Increased from 24px */
  font-weight: 700; /* Increased from 600 */
  color: #2a2a2a; /* Darker color for better readability */
  letter-spacing: 1.5px;
  margin-bottom: 35px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

/* Adding a subtle pattern overlay for more texture */
.app::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 0;
}

.venues-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  padding: 20px 0;
}

@media (max-width: 768px) {
  .venues-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}
  




