.footer {
  position: relative;
  padding: 80px 0 20px;
  margin-top: 50px;
  background-image: url('https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2940&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: #fff;
}

.footer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 24, 16, 0.9);
  backdrop-filter: blur(2px);
}

.footer-content {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  padding: 0 20px;
}

.footer-section h3 {
  color: #d4af37;
  font-size: 1.8rem;
  margin-bottom: 25px;
  font-family: 'Lato', sans-serif;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  padding-bottom: 15px;
  display: inline-block;
  width: auto;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #d4af37;
}

.footer-section p {
  margin: 15px 0;
  color: #fff;
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.footer-section p:hover {
  transform: translateX(5px);
}

.footer-section i {
  margin-right: 12px;
  color: #d4af37;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 15px;
}

.footer-section ul li a {
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  display: inline-block;
}

.footer-section ul li a:hover {
  color: #d4af37;
  transform: translateX(5px);
}

.social-links {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-top: 20px;
}

.social-link {
  position: relative;
  color: #fff;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  width: 50px;
  height: 50px;
  background: rgba(212, 175, 55, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  overflow: hidden;
  text-decoration: none; /* Added this line to remove underline */
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.social-link:hover {
  color: #d4af37;
  border-color: #d4af37;
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
  text-decoration: none; /* Added this line to ensure no underline on hover */
}

.social-link:hover::before {
  opacity: 1;
}

.social-link i {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.social-tooltip {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(212, 175, 55, 0.9);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.social-tooltip::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(212, 175, 55, 0.9);
}

.social-link:hover .social-tooltip {
  opacity: 1;
  visibility: visible;
  bottom: -40px;
}

/* Updated responsive styles for social links */
@media screen and (max-width: 768px) {
  .social-links {
    justify-content: center;
    gap: 20px;
  }

  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }
  
  .social-tooltip {
    display: none; /* Hide tooltips on mobile */
  }
}

/* Enhanced floating animation */
@keyframes float {
  0% { 
    transform: translateY(0px) scale(1);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
  }
  50% { 
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 20px rgba(212, 175, 55, 0.4);
  }
  100% { 
    transform: translateY(0px) scale(1);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
  }
}

.social-link:hover i {
  animation: float 1.5s ease-in-out infinite;
}

.footer-bottom {
  position: relative;
  text-align: center;
  margin-top: 60px;
  padding-top: 20px;
  border-top: 1px solid rgba(212, 175, 55, 0.3);
}

.footer-bottom p {
  color: #fff;
  font-size: 1rem;
}

/* Responsive Design */
@media screen and (max-width: 992px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .footer {
    padding: 60px 0 20px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .footer-section {
    text-align: center;
  }
  
  .footer-section h3 {
    text-align: center;
  }

  .footer-section h3::after {
    left: 0;
    width: 100%;
    transform: none;
  }
  
  .social-links {
    justify-content: center;
  }
}

/* Animation for social icons */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

.social-links a:hover i {
  animation: float 1s ease-in-out infinite;
}



