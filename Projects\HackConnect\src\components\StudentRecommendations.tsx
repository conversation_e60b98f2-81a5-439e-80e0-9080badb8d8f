
import React from 'react';
import { Star, MapPin, Code, Trophy, MessageCircle, UserPlus } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface Student {
  id: string;
  name: string;
  avatar: string;
  college: string;
  location: string;
  skills: string[];
  rating: number;
  completedHackathons: number;
  achievements: string[];
  bio: string;
  isOnline: boolean;
  responseTime: string;
}

const mockStudents: Student[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    college: 'Stanford University',
    location: 'San Francisco, CA',
    skills: ['React', 'Node.js', 'Python', 'AI/ML'],
    rating: 4.9,
    completedHackathons: 12,
    achievements: ['Winner - AI Challenge 2024', 'Best UI/UX - TechCrunch'],
    bio: 'Full-stack developer passionate about AI and creating impactful solutions.',
    isOnline: true,
    responseTime: '< 1 hour'
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    college: 'MIT',
    location: 'Boston, MA',
    skills: ['UI/UX', 'Figma', 'Frontend', 'Mobile'],
    rating: 4.8,
    completedHackathons: 8,
    achievements: ['Design Excellence Award', 'Most Innovative UI'],
    bio: 'Designer and frontend developer with expertise in creating beautiful user experiences.',
    isOnline: false,
    responseTime: '< 4 hours'
  },
  {
    id: '3',
    name: 'David Park',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    college: 'UC Berkeley',
    location: 'Berkeley, CA',
    skills: ['Java', 'Spring', 'Docker', 'AWS'],
    rating: 4.7,
    completedHackathons: 15,
    achievements: ['Backend Champion', 'Cloud Architecture Award'],
    bio: 'Backend specialist with strong experience in scalable system architecture.',
    isOnline: true,
    responseTime: '< 30 min'
  }
];

const StudentRecommendations = () => {
  return (
    <div className="max-w-7xl mx-auto mt-16">
      <div className="text-center mb-12">
        <div className="inline-flex items-center bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full mb-4">
          <Star className="w-4 h-4 mr-2" />
          Featured Developers
        </div>
        <h3 className="text-3xl font-bold text-gray-800 mb-4">Top Rated Teammates</h3>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Connect with highly-rated developers who have proven track records in hackathons
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {mockStudents.map((student, index) => (
          <Card 
            key={student.id}
            className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white/90 backdrop-blur-md border-0 shadow-lg overflow-hidden relative"
            style={{ animationDelay: `${index * 200}ms` }}
          >
            {/* Online Status Indicator */}
            <div className="absolute top-4 right-4 z-10">
              <div className={`w-3 h-3 rounded-full ${student.isOnline ? 'bg-green-400' : 'bg-gray-400'} border-2 border-white shadow-lg`}>
                {student.isOnline && (
                  <div className="w-3 h-3 rounded-full bg-green-400 animate-ping absolute"></div>
                )}
              </div>
            </div>

            <CardHeader className="text-center pb-4">
              <div className="relative mb-4">
                <Avatar className="w-24 h-24 mx-auto border-4 border-white shadow-lg">
                  <AvatarImage src={student.avatar} alt={student.name} />
                  <AvatarFallback className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xl">
                    {student.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                {student.isOnline && (
                  <div className="absolute -bottom-1 -right-1 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                    Online
                  </div>
                )}
              </div>
              
              <h3 className="text-xl font-bold text-gray-800 mb-1">{student.name}</h3>
              <p className="text-sm text-gray-600 mb-2">{student.college}</p>
              
              {/* Rating */}
              <div className="flex items-center justify-center mb-2">
                <div className="flex items-center bg-yellow-50 px-3 py-1 rounded-full border border-yellow-200">
                  <Star className="w-4 h-4 text-yellow-500 fill-current mr-1" />
                  <span className="text-sm font-semibold text-yellow-700">{student.rating}</span>
                  <span className="text-xs text-gray-500 ml-1">({student.completedHackathons} hackathons)</span>
                </div>
              </div>

              <div className="flex items-center justify-center text-sm text-gray-600 mb-3">
                <MapPin className="w-4 h-4 mr-1" />
                {student.location}
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Bio */}
              <p className="text-sm text-gray-700 mb-4 text-center">{student.bio}</p>

              {/* Skills */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Code className="w-4 h-4 mr-2 text-purple-500" />
                  <span className="text-sm font-medium text-gray-700">Skills</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {student.skills.map((skill, idx) => (
                    <Badge key={idx} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Achievements */}
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Trophy className="w-4 h-4 mr-2 text-orange-500" />
                  <span className="text-sm font-medium text-gray-700">Recent Achievements</span>
                </div>
                <div className="space-y-1">
                  {student.achievements.slice(0, 2).map((achievement, idx) => (
                    <p key={idx} className="text-xs text-gray-600 bg-orange-50 px-2 py-1 rounded border border-orange-200">
                      🏆 {achievement}
                    </p>
                  ))}
                </div>
              </div>

              {/* Response Time */}
              <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Response Time</span>
                  <span className="text-sm font-bold text-blue-600">{student.responseTime}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1 group-hover:border-purple-300 transition-colors"
                >
                  <MessageCircle className="w-4 h-4 mr-1" />
                  Message
                </Button>
                <Button 
                  size="sm" 
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  <UserPlus className="w-4 h-4 mr-1" />
                  Connect
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Call to Action */}
      <div className="text-center mt-12">
        <Button 
          size="lg"
          variant="outline"
          className="px-8 py-4 text-lg border-2 border-purple-300 hover:bg-purple-50 hover:border-purple-400 transition-all duration-300"
        >
          View All Developers
        </Button>
      </div>
    </div>
  );
};

export default StudentRecommendations;
