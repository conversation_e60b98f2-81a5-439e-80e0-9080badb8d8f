import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import DarkModeToggle from '../ui/DarkModeToggle';

function Header() {
  const { user, logout } = useAuth();

  return (
    <header className="bg-green-600 dark:bg-green-800 text-white p-4">
      <nav className="container mx-auto flex justify-between items-center">
        <Link to="/" className="text-2xl font-bold">AgroAI</Link>
        
        <div className="flex items-center gap-4">
          <DarkModeToggle />
          {user ? (
            <>
              <Link to="/dashboard" className="hover:text-green-200">Dashboard</Link>
              <button onClick={logout} className="hover:text-green-200">Logout</button>
            </>
          ) : (
            <>
              <Link to="/login" className="hover:text-green-200">Login</Link>
              <Link to="/signup" className="hover:text-green-200">Sign Up</Link>
            </>
          )}
        </div>
      </nav>
    </header>
  );
}

export default Header;