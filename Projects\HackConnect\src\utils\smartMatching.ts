export interface UserProfile {
  id: string;
  name: string;
  college: string;
  state: string;
  skills: string[];
  branch: string;
  avatar?: string;
  achievements?: string;
  status: 'available' | 'in-team' | 'busy';
  mutualInterests?: string[];
}

export interface MatchResult {
  profile: UserProfile;
  score: number;
  matchReasons: string[];
}

// Calculate similarity score between two profiles
export const calculateSimilarityScore = (
  userProfile: Partial<UserProfile>,
  candidateProfile: UserProfile
): number => {
  try {
    let score = 0;
    let totalWeight = 0;

    // College match (weight: 0.3)
    if (userProfile.college && candidateProfile.college) {
      totalWeight += 0.3;
      if (userProfile.college.toLowerCase().trim() === candidateProfile.college.toLowerCase().trim()) {
        score += 0.3;
      }
    }

    // State match (weight: 0.2)
    if (userProfile.state && candidateProfile.state) {
      totalWeight += 0.2;
      if (userProfile.state.toLowerCase().trim() === candidateProfile.state.toLowerCase().trim()) {
        score += 0.2;
      }
    }

    // Skills match (weight: 0.4)
    if (userProfile.skills && candidateProfile.skills && 
        Array.isArray(userProfile.skills) && Array.isArray(candidateProfile.skills)) {
      totalWeight += 0.4;
      const userSkills = userProfile.skills
        .filter(skill => typeof skill === 'string' && skill.trim())
        .map(s => s.toLowerCase().trim());
      const candidateSkills = candidateProfile.skills
        .filter(skill => typeof skill === 'string' && skill.trim())
        .map(s => s.toLowerCase().trim());
      
      if (userSkills.length > 0 && candidateSkills.length > 0) {
        const commonSkills = userSkills.filter(skill => 
          candidateSkills.some(cs => cs.includes(skill) || skill.includes(cs))
        );
        const skillMatchRatio = commonSkills.length / Math.max(userSkills.length, candidateSkills.length);
        score += 0.4 * skillMatchRatio;
      }
    }

    // Branch match (weight: 0.1)
    if (userProfile.branch && candidateProfile.branch) {
      totalWeight += 0.1;
      if (userProfile.branch.toLowerCase().trim() === candidateProfile.branch.toLowerCase().trim()) {
        score += 0.1;
      }
    }

    const finalScore = totalWeight > 0 ? (score / totalWeight) * 100 : 0;
    return Math.min(100, Math.max(0, finalScore)); // Ensure score is between 0-100
  } catch (error) {
    console.error('Error calculating similarity score:', error);
    return 0;
  }
};

// Generate match reasons for display
export const generateMatchReasons = (
  userProfile: Partial<UserProfile>,
  candidateProfile: UserProfile
): string[] => {
  try {
    const reasons: string[] = [];

    if (userProfile.college && candidateProfile.college && 
        userProfile.college.toLowerCase().trim() === candidateProfile.college.toLowerCase().trim()) {
      reasons.push(`Same college: ${candidateProfile.college}`);
    }

    if (userProfile.state && candidateProfile.state && 
        userProfile.state.toLowerCase().trim() === candidateProfile.state.toLowerCase().trim()) {
      reasons.push(`Same state: ${candidateProfile.state}`);
    }

    if (userProfile.branch && candidateProfile.branch && 
        userProfile.branch.toLowerCase().trim() === candidateProfile.branch.toLowerCase().trim()) {
      reasons.push(`Same branch: ${candidateProfile.branch}`);
    }

    if (userProfile.skills && candidateProfile.skills && 
        Array.isArray(userProfile.skills) && Array.isArray(candidateProfile.skills)) {
      const userSkills = userProfile.skills
        .filter(skill => typeof skill === 'string' && skill.trim())
        .map(s => s.toLowerCase().trim());
      const candidateSkills = candidateProfile.skills
        .filter(skill => typeof skill === 'string' && skill.trim())
        .map(s => s.toLowerCase().trim());
      
      if (userSkills.length > 0 && candidateSkills.length > 0) {
        const commonSkills = userSkills.filter(skill => 
          candidateSkills.some(cs => cs.includes(skill) || skill.includes(cs))
        );
        if (commonSkills.length > 0) {
          const displaySkills = commonSkills.slice(0, 3).map(skill => 
            candidateProfile.skills?.find(s => s.toLowerCase().includes(skill)) || skill
          );
          reasons.push(`Shared skills: ${displaySkills.join(', ')}`);
        }
      }
    }

    return reasons;
  } catch (error) {
    console.error('Error generating match reasons:', error);
    return [];
  }
};

// Main smart matching function
export const findSmartMatches = (
  userProfile: Partial<UserProfile>,
  candidateProfiles: UserProfile[],
  limit: number = 6
): MatchResult[] => {
  try {
    // Validate inputs
    if (!candidateProfiles || candidateProfiles.length === 0) {
      console.warn('No candidate profiles provided for matching');
      return [];
    }

    if (!userProfile) {
      console.warn('No user profile provided for matching');
      return [];
    }

    // Filter out unavailable profiles and calculate matches
    const availableCandidates = candidateProfiles.filter(profile => 
      profile && profile.status === 'available' && profile.id
    );

    if (availableCandidates.length === 0) {
      console.warn('No available candidate profiles found');
      return [];
    }

    const matches = availableCandidates
      .map(profile => {
        try {
          const score = calculateSimilarityScore(userProfile, profile);
          const matchReasons = generateMatchReasons(userProfile, profile);
          
          return {
            profile,
            score: isNaN(score) ? 0 : score,
            matchReasons: matchReasons || []
          };
        } catch (error) {
          console.error('Error calculating match for profile:', profile.id, error);
          return null;
        }
      })
      .filter((match): match is MatchResult => match !== null)
      .sort((a, b) => b.score - a.score)
      .slice(0, Math.max(1, limit));

    console.log(`Found ${matches.length} matches for user profile`);
    return matches;
  } catch (error) {
    console.error('Error in findSmartMatches:', error);
    return [];
  }
};
