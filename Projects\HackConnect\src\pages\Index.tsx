
import React from 'react';
import HeroSection from '@/components/HeroSection';
import HackathonListings from '@/components/HackathonListings';
import TeamMaker from '@/components/TeamMaker';
import StudentRecommendations from '@/components/StudentRecommendations';
import Footer from '@/components/Footer';

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <HeroSection />
      
      {/* Explore Hackathons Section */}
      <section id="explore-hackathons" className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">Explore Hackathons</h2>
            <p className="text-xl text-gray-600">Discover amazing hackathons happening around the world</p>
          </div>
          <HackathonListings />
        </div>
      </section>

      {/* Find Teammates Section */}
      <section id="find-teammates" className="py-20 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">Find Teammates</h2>
            <p className="text-xl text-gray-600">Connect with talented developers and build amazing teams</p>
          </div>
          <TeamMaker />
          <StudentRecommendations />
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
