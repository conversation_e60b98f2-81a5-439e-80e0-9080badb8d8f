import React from "react";
import "./index.css";

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-gold-50">
      <div className="container mx-auto px-4 py-8">
        {/* Test Header */}
        <h1 className="text-4xl font-serif text-primary-600 text-center mb-8">
          Wedding Planner ✨
        </h1>

        {/* Test Card */}
        <div className="card max-w-md mx-auto p-6">
          <h2 className="text-2xl font-serif text-gray-800 mb-4">
            TailwindCSS Test
          </h2>
          <p className="text-gray-600 mb-6">
            If you can see this styled card with custom colors, TailwindCSS is
            working perfectly!
          </p>

          {/* Test Buttons */}
          <div className="space-y-3">
            <button className="btn-primary w-full">
              Primary Button (Pink)
            </button>
            <button className="btn-secondary w-full">
              Secondary Button (Gold)
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
