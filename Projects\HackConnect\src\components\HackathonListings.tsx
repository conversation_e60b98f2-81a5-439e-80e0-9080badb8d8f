
import React, { useState } from 'react';
import { Calendar, MapPin, Users, Trophy, Clock, ExternalLink, Search, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Hackathon {
  id: string;
  title: string;
  organizer: string;
  date: string;
  location: string;
  mode: 'Online' | 'Offline' | 'Hybrid';
  prize: string;
  participants: number;
  maxParticipants: number;
  description: string;
  tags: string[];
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  image: string;
  registrationDeadline: string;
}

const mockHackathons: Hackathon[] = [
  {
    id: '1',
    title: 'AI Innovation Challenge 2024',
    organizer: 'TechCorp',
    date: '2024-07-15',
    location: 'San Francisco, CA',
    mode: 'Hybrid',
    prize: '$50,000',
    participants: 1250,
    maxParticipants: 2000,
    description: 'Build the next generation of AI applications that solve real-world problems.',
    tags: ['AI', 'Machine Learning', 'Innovation'],
    difficulty: 'Advanced',
    image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=200&fit=crop',
    registrationDeadline: '2024-07-01'
  },
  {
    id: '2',
    title: 'Green Tech Hackathon',
    organizer: 'EcoVentures',
    date: '2024-06-28',
    location: 'Online',
    mode: 'Online',
    prize: '$25,000',
    participants: 800,
    maxParticipants: 1500,
    description: 'Create sustainable technology solutions for environmental challenges.',
    tags: ['Sustainability', 'Environment', 'IoT'],
    difficulty: 'Intermediate',
    image: 'https://images.unsplash.com/photo-**********-3c8c76ca7d13?w=400&h=200&fit=crop',
    registrationDeadline: '2024-06-20'
  },
  {
    id: '3',
    title: 'FinTech Revolution',
    organizer: 'Banking Plus',
    date: '2024-08-10',
    location: 'New York, NY',
    mode: 'Offline',
    prize: '$75,000',
    participants: 950,
    maxParticipants: 1200,
    description: 'Transform the future of financial services with innovative solutions.',
    tags: ['FinTech', 'Blockchain', 'Security'],
    difficulty: 'Advanced',
    image: 'https://images.unsplash.com/photo-**********-4b87b5e36e44?w=400&h=200&fit=crop',
    registrationDeadline: '2024-07-25'
  }
];

const HackathonListings = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filteredHackathons = mockHackathons.filter(hackathon => {
    const matchesSearch = hackathon.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hackathon.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = selectedFilter === 'all' || 
                         hackathon.mode.toLowerCase() === selectedFilter ||
                         hackathon.difficulty.toLowerCase() === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Advanced': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'Online': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Offline': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Hybrid': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Search and Filter Section */}
      <div className="mb-8 bg-white/80 backdrop-blur-md rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search hackathons by name or technology..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/90"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-gray-500" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className="px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/90"
            >
              <option value="all">All Hackathons</option>
              <option value="online">Online</option>
              <option value="offline">Offline</option>
              <option value="hybrid">Hybrid</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </div>
      </div>

      {/* Hackathon Cards Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredHackathons.map((hackathon, index) => (
          <Card 
            key={hackathon.id} 
            className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white/90 backdrop-blur-md border-0 shadow-lg overflow-hidden"
            style={{ animationDelay: `${index * 150}ms` }}
          >
            {/* Image Header */}
            <div className="relative overflow-hidden">
              <img 
                src={hackathon.image} 
                alt={hackathon.title}
                className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
              />
              <div className="absolute top-4 left-4">
                <Badge className={`${getModeColor(hackathon.mode)} border font-medium`}>
                  {hackathon.mode}
                </Badge>
              </div>
              <div className="absolute top-4 right-4">
                <Badge className={`${getDifficultyColor(hackathon.difficulty)} border font-medium`}>
                  {hackathon.difficulty}
                </Badge>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>

            <CardHeader className="pb-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-xl font-bold text-gray-800 group-hover:text-purple-600 transition-colors">
                  {hackathon.title}
                </h3>
                <Trophy className="w-5 h-5 text-yellow-500 flex-shrink-0" />
              </div>
              <p className="text-sm text-gray-600 font-medium">{hackathon.organizer}</p>
              <p className="text-sm text-gray-700 mt-2 line-clamp-2">{hackathon.description}</p>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Event Details */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="w-4 h-4 mr-2 text-purple-500" />
                  {new Date(hackathon.date).toLocaleDateString('en-US', { 
                    month: 'long', 
                    day: 'numeric', 
                    year: 'numeric' 
                  })}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="w-4 h-4 mr-2 text-purple-500" />
                  {hackathon.location}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="w-4 h-4 mr-2 text-purple-500" />
                  {hackathon.participants.toLocaleString()} / {hackathon.maxParticipants.toLocaleString()} participants
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="w-4 h-4 mr-2 text-purple-500" />
                  Register by {new Date(hackathon.registrationDeadline).toLocaleDateString()}
                </div>
              </div>

              {/* Prize */}
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-3 rounded-lg mb-4 border border-yellow-200">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Prize Pool</span>
                  <span className="text-lg font-bold text-orange-600">{hackathon.prize}</span>
                </div>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {hackathon.tags.map((tag, idx) => (
                  <Badge key={idx} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-xs text-gray-600 mb-1">
                  <span>Registration Progress</span>
                  <span>{Math.round((hackathon.participants / hackathon.maxParticipants) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(hackathon.participants / hackathon.maxParticipants) * 100}%` }}
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1 group-hover:border-purple-300 transition-colors"
                >
                  Learn More
                </Button>
                <Button 
                  size="sm" 
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  Register
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* No Results Message */}
      {filteredHackathons.length === 0 && (
        <div className="text-center py-16">
          <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center">
            <Search className="w-12 h-12 text-purple-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No hackathons found</h3>
          <p className="text-gray-500 mb-4">Try adjusting your search terms or filters</p>
          <Button 
            onClick={() => { setSearchTerm(''); setSelectedFilter('all'); }}
            variant="outline"
            className="hover:bg-purple-50"
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};

export default HackathonListings;
